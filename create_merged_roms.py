#!/usr/bin/env python3
"""
<PERSON>ript to create merged ROM files for SF2 Rust port.
Based on the mt2-merge.sh script but adapted for our ROM set.
"""

import os
import sys

def interleave_files(output_name, step, *input_files):
    """
    Interleave multiple files with a given step size.
    This mimics the behavior of the interleave tool.
    """
    print(f"Creating {output_name} by interleaving: {', '.join(input_files)}")
    
    # Read all input files
    file_data = []
    for filename in input_files:
        if not os.path.exists(filename):
            print(f"Error: {filename} not found!")
            return False
        with open(filename, 'rb') as f:
            file_data.append(f.read())
    
    # Check all files are the same size
    if len(set(len(data) for data in file_data)) > 1:
        print(f"Warning: Input files have different sizes: {[len(data) for data in file_data]}")
    
    # Interleave the data
    with open(output_name, 'wb') as output:
        max_len = max(len(data) for data in file_data)
        for i in range(0, max_len, step):
            for data in file_data:
                if i < len(data):
                    chunk = data[i:i+step]
                    output.write(chunk)
    
    print(f"Created {output_name} ({os.path.getsize(output_name)} bytes)")
    return True

def create_sf2gfx():
    """Create sf2gfx.bin from graphics ROMs"""
    print("Creating sf2gfx.bin...")
    
    # Change to extracted_roms directory
    os.chdir('extracted_roms')
    
    # Create intermediate files (interleave with step=2)
    if not interleave_files('gint1', 2, 'sf2-5m.4a', 'sf2-7m.6a', 'sf2-1m.3a', 'sf2-3m.5a'):
        return False
    if not interleave_files('gint2', 2, 'sf2-6m.4c', 'sf2-8m.6c', 'sf2-2m.3c', 'sf2-4m.5c'):
        return False
    if not interleave_files('gint3', 2, 'sf2-13m.4d', 'sf2-15m.6d', 'sf2-9m.3d', 'sf2-11m.5d'):
        return False
    
    # Concatenate the intermediate files
    print("Concatenating intermediate files...")
    with open('../sf2gfx.bin', 'wb') as output:
        for filename in ['gint1', 'gint2', 'gint3']:
            with open(filename, 'rb') as f:
                output.write(f.read())
    
    # Clean up intermediate files
    for filename in ['gint1', 'gint2', 'gint3']:
        os.remove(filename)
    
    os.chdir('..')
    print(f"Created sf2gfx.bin ({os.path.getsize('sf2gfx.bin')} bytes)")
    return True

def create_allroms():
    """Create allroms.bin from code ROMs"""
    print("Creating allroms.bin...")
    
    # We have different ROM names than the original script expects
    # Let's try to map our ROMs to the expected pattern
    os.chdir('extracted_roms')
    
    # For the code ROMs, we need to figure out the correct mapping
    # The original script expects: sf2u.30a, sf2u.37a, sf2u.31a, sf2u.38a, sf2u.28a, sf2u.35a, sf2_29a.bin, sf2_36a.bin
    # We have: sf2e_30g.11e, sf2e_37g.11f, sf2e_31g.12e, sf2e_38g.12f, sf2e_28g.9e, sf2e_35g.9f, sf2_29b.10e, sf2_36b.10f
    
    # Try to create with our available ROMs
    if not interleave_files('int1', 1, 'sf2e_30g.11e', 'sf2e_37g.11f'):
        return False
    if not interleave_files('int2', 1, 'sf2e_31g.12e', 'sf2e_38g.12f'):
        return False
    if not interleave_files('int3', 1, 'sf2e_28g.9e', 'sf2e_35g.9f'):
        return False
    if not interleave_files('int4', 1, 'sf2_29b.10e', 'sf2_36b.10f'):
        return False
    
    # Concatenate the intermediate files
    print("Concatenating intermediate files...")
    with open('../allroms.bin', 'wb') as output:
        for filename in ['int1', 'int2', 'int3', 'int4']:
            with open(filename, 'rb') as f:
                output.write(f.read())
    
    # Clean up intermediate files
    for filename in ['int1', 'int2', 'int3', 'int4']:
        os.remove(filename)
    
    os.chdir('..')
    print(f"Created allroms.bin ({os.path.getsize('allroms.bin')} bytes)")
    return True

def main():
    if not os.path.exists('extracted_roms'):
        print("Error: extracted_roms directory not found!")
        print("Please run: unzip -o roms/sf2.zip -d extracted_roms")
        return 1
    
    print("Creating merged ROM files for SF2 Rust port...")
    
    # Create graphics ROM
    if not create_sf2gfx():
        print("Failed to create sf2gfx.bin")
        return 1
    
    # Create code ROM
    if not create_allroms():
        print("Failed to create allroms.bin")
        return 1
    
    print("\nSuccess! Created merged ROM files:")
    print(f"  sf2gfx.bin: {os.path.getsize('sf2gfx.bin'):,} bytes")
    print(f"  allroms.bin: {os.path.getsize('allroms.bin'):,} bytes")
    print("\nThese files are ready for use with the SF2 Rust port.")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
