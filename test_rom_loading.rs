use sf2_assets::rom_loader::RomLoader;
use sf2_assets::sprite_extractor::SpriteExtractor;

fn main() {
    println!("Testing SF2 ROM loading with new format...");
    
    // Test ROM loading
    let mut loader = RomLoader::new();
    match loader.load_all_roms() {
        Ok(rom_set) => {
            println!("✓ Successfully loaded ROM files:");
            println!("  Graphics ROM: {} bytes", rom_set.graphics_rom.data.len());
            println!("  Code ROM: {} bytes", rom_set.code_rom.data.len());
            
            // Test sprite extraction
            let extractor = SpriteExtractor::new();
            match extractor.extract_character_sprites(&rom_set, sf2_types::FighterId::Ryu) {
                Ok(sprites) => {
                    println!("✓ Successfully extracted {} sprites for Ryu", sprites.len());
                    
                    // Check first sprite
                    if let Some(first_sprite) = sprites.first() {
                        println!("  First sprite: {}x{} pixels, {} bytes", 
                                first_sprite.width, first_sprite.height, first_sprite.data.len());
                    }
                }
                Err(e) => {
                    println!("✗ Failed to extract sprites: {}", e);
                }
            }
        }
        Err(e) => {
            println!("✗ Failed to load ROMs: {}", e);
        }
    }
}
