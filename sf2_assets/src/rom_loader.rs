//! # ROM Loader
//!
//! Handles loading and validation of Street Fighter II ROM files.
//!
//! This module implements comprehensive ROM loading for SF2 World Warriors,
//! supporting both individual ROM files and combined ROM formats.
//! It handles graphics ROMs, code ROMs, audio ROMs, and provides validation.

use std::path::{Path, PathBuf};
use std::fs;
use std::io::Read;
use thiserror::Error;
use log::{info, warn, error, debug};
use serde::{Serialize, Deserialize};
use zip::ZipArchive;

/// Errors that can occur during ROM loading
#[derive(Error, Debug)]
pub enum RomLoadError {
    #[error("File not found: {path}")]
    FileNotFound { path: String },

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Invalid ROM size: expected {expected}, got {actual}")]
    InvalidSize { expected: usize, actual: usize },

    #[error("Invalid ROM checksum: expected {expected:08x}, got {actual:08x}")]
    InvalidChecksum { expected: u32, actual: u32 },

    #[error("Unsupported ROM version: {version}")]
    UnsupportedVersion { version: String },

    #[error("ROM validation failed: {reason}")]
    ValidationFailed { reason: String },

    #[error("Missing required ROM files: {files:?}")]
    MissingRomFiles { files: Vec<String> },

    #[error("ZIP archive error: {0}")]
    ZipError(#[from] zip::result::ZipError),
}

/// Information about a specific ROM file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RomInfo {
    pub name: String,
    pub size: usize,
    pub checksum: Option<u32>, // Optional for development
    pub version: String,
    pub rom_type: RomType,
}

/// Type of ROM file
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RomType {
    Graphics,
    Code,
    Audio,
    Program,
    Sound,
}

/// SF2 ROM file definitions based on original C99 implementation format
/// Expects pre-processed interleaved ROM files as created by mt2-merge.sh
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SF2RomSet {
    /// Graphics ROM file (sf2gfx.bin) - interleaved and concatenated graphics data
    pub graphics_rom: RomInfo,
    /// Code ROM file (allroms.bin) - interleaved and concatenated code data
    pub code_rom: RomInfo,
}

impl SF2RomSet {
    /// Get SF2 World Warriors ROM set definition for original format
    /// This matches the format expected by the original C99 implementation
    pub fn sf2_world_warriors() -> Self {
        Self {
            graphics_rom: RomInfo {
                name: "sf2gfx.bin".to_string(),
                size: 0x600000, // 6MB total - matches original C99 implementation
                checksum: None, // Will validate at runtime if available
                version: "World Warriors".to_string(),
                rom_type: RomType::Graphics,
            },
            code_rom: RomInfo {
                name: "allroms.bin".to_string(),
                size: 0x400000, // 4MB estimated - will be validated at runtime
                checksum: None, // Will validate at runtime if available
                version: "World Warriors".to_string(),
                rom_type: RomType::Code,
            },
        }
    }
}

/// ROM loader for Street Fighter II
pub struct RomLoader {
    rom_directory: PathBuf,
    rom_set: SF2RomSet,
}

impl RomLoader {
    /// Create a new ROM loader
    pub fn new<P: AsRef<Path>>(rom_directory: P) -> Self {
        Self {
            rom_directory: rom_directory.as_ref().to_path_buf(),
            rom_set: SF2RomSet::sf2_world_warriors(),
        }
    }

    /// Create ROM loader with custom ROM set
    pub fn with_rom_set<P: AsRef<Path>>(rom_directory: P, rom_set: SF2RomSet) -> Self {
        Self {
            rom_directory: rom_directory.as_ref().to_path_buf(),
            rom_set,
        }
    }
    
    /// Load all ROM files in original C99 format (sf2gfx.bin and allroms.bin)
    pub fn load_all_roms(&self) -> Result<LoadedRoms, RomLoadError> {
        info!("Loading ROM files from: {}", self.rom_directory.display());
        info!("Expected format: sf2gfx.bin (graphics) and allroms.bin (code)");

        // Load graphics ROM (sf2gfx.bin)
        let graphics_data = self.load_rom_file(&self.rom_set.graphics_rom)?;
        info!("Loaded graphics ROM: {} bytes", graphics_data.len());

        // Validate graphics ROM structure (matches original C99 implementation)
        if graphics_data.len() != 0x600000 {
            warn!("Graphics ROM size {} doesn't match expected 0x600000 (6MB)", graphics_data.len());
        }

        // Load code ROM (allroms.bin)
        let code_data = self.load_rom_file(&self.rom_set.code_rom)?;
        info!("Loaded code ROM: {} bytes", code_data.len());

        // Extract sprite and tile data from graphics ROM (following original C99 layout)
        // sprites = gfxData[..<0x480000] (first 4.5MB)
        // tiles = gfxData[0x400000...] (from 4MB onwards)
        let sprite_data_end = std::cmp::min(0x480000, graphics_data.len());
        let tile_data_start = std::cmp::min(0x400000, graphics_data.len());

        info!("Graphics ROM layout:");
        info!("  Sprites: 0x000000 - 0x{:06X} ({} bytes)", sprite_data_end, sprite_data_end);
        info!("  Tiles:   0x{:06X} - 0x{:06X} ({} bytes)",
              tile_data_start, graphics_data.len(), graphics_data.len() - tile_data_start);

        info!("Successfully loaded ROM files in original C99 format");

        Ok(LoadedRoms {
            graphics_rom: graphics_data,
            code_rom: code_data,
            audio_rom: None, // Audio data is embedded in graphics ROM for SF2
            sound_rom: None, // Sound data is embedded in code ROM for SF2
        })
    }
    
    /// Load a specific ROM file
    fn load_rom_file(&self, rom_info: &RomInfo) -> Result<Vec<u8>, RomLoadError> {
        let file_path = self.rom_directory.join(&rom_info.name);

        debug!("Loading ROM file: {}", file_path.display());

        // First try to load as individual file
        if file_path.exists() {
            let data = fs::read(&file_path)?;
            return self.validate_rom_data(rom_info, data);
        }

        // If individual file doesn't exist, try to find it in ZIP files
        if let Ok(data) = self.load_from_zip_archive(&rom_info.name) {
            return self.validate_rom_data(rom_info, data);
        }

        Err(RomLoadError::FileNotFound {
            path: file_path.to_string_lossy().to_string(),
        })
    }

    /// Load ROM file from ZIP archive
    fn load_from_zip_archive(&self, rom_name: &str) -> Result<Vec<u8>, RomLoadError> {
        // Look for common MAME ZIP file names
        let zip_candidates = [
            "sf2.zip",
            "sf2ww.zip",
            "streetfighter2.zip",
            "sf2_world_warriors.zip",
        ];

        for zip_name in &zip_candidates {
            let zip_path = self.rom_directory.join(zip_name);
            if zip_path.exists() {
                debug!("Checking ZIP archive: {}", zip_path.display());

                match self.extract_from_zip(&zip_path, rom_name) {
                    Ok(data) => {
                        info!("Found {} in ZIP archive: {}", rom_name, zip_name);
                        return Ok(data);
                    }
                    Err(_) => continue, // Try next ZIP file
                }
            }
        }

        // Also check for any ZIP file in the directory
        if let Ok(entries) = fs::read_dir(&self.rom_directory) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.extension().and_then(|s| s.to_str()) == Some("zip") {
                    debug!("Checking ZIP archive: {}", path.display());

                    if let Ok(data) = self.extract_from_zip(&path, rom_name) {
                        info!("Found {} in ZIP archive: {}", rom_name, path.display());
                        return Ok(data);
                    }
                }
            }
        }

        Err(RomLoadError::FileNotFound {
            path: format!("{} (not found in any ZIP archive)", rom_name),
        })
    }

    /// Extract a specific file from a ZIP archive
    fn extract_from_zip(&self, zip_path: &Path, rom_name: &str) -> Result<Vec<u8>, RomLoadError> {
        let file = fs::File::open(zip_path)?;
        let mut archive = ZipArchive::new(file)?;

        // Try to find the ROM file in the archive
        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            if file.name() == rom_name {
                let mut data = Vec::with_capacity(file.size() as usize);
                file.read_to_end(&mut data)?;
                return Ok(data);
            }
        }

        Err(RomLoadError::FileNotFound {
            path: format!("{} in {}", rom_name, zip_path.display()),
        })
    }

    /// Validate ROM data after loading
    fn validate_rom_data(&self, rom_info: &RomInfo, data: Vec<u8>) -> Result<Vec<u8>, RomLoadError> {

        // Validate file size
        if data.len() != rom_info.size {
            warn!(
                "ROM file {} has unexpected size: {} (expected {})",
                rom_info.name, data.len(), rom_info.size
            );
            // Don't fail on size mismatch for now, just warn
        }

        // Calculate and validate checksum if provided
        if let Some(expected_checksum) = rom_info.checksum {
            let checksum = calculate_checksum(&data);
            if checksum != expected_checksum {
                warn!(
                    "ROM file {} has unexpected checksum: {:08x} (expected {:08x})",
                    rom_info.name, checksum, expected_checksum
                );
                // Don't fail on checksum mismatch for now, just warn
                // This allows for different ROM versions during development
            }
        }

        debug!(
            "Loaded ROM file: {} ({} bytes)",
            rom_info.name, data.len()
        );

        Ok(data)
    }

    /// Check if all required ROM files exist in original C99 format
    pub fn validate_rom_files(&self) -> Result<(), RomLoadError> {
        let mut missing_files = Vec::new();

        // Check for sf2gfx.bin
        let graphics_path = self.rom_directory.join(&self.rom_set.graphics_rom.name);
        if !graphics_path.exists() {
            missing_files.push(self.rom_set.graphics_rom.name.clone());
        }

        // Check for allroms.bin
        let code_path = self.rom_directory.join(&self.rom_set.code_rom.name);
        if !code_path.exists() {
            missing_files.push(self.rom_set.code_rom.name.clone());
        }

        if !missing_files.is_empty() {
            error!("Missing required ROM files: {:?}", missing_files);
            error!("Expected files in original C99 format:");
            error!("  - sf2gfx.bin (6MB graphics ROM, interleaved)");
            error!("  - allroms.bin (4MB code ROM, interleaved)");
            error!("");
            error!("To create these files from MAME ROM set:");
            error!("  1. Get MAME sf2 ROM set (sf2.zip or individual files)");
            error!("  2. Use the interleave tool and bin/mt2-merge.sh script");
            error!("  3. Copy sf2gfx.bin and allroms.bin to ROM directory");
            return Err(RomLoadError::MissingRomFiles { files: missing_files });
        }

        info!("All required ROM files found in original C99 format");
        info!("  - sf2gfx.bin: {}", graphics_path.display());
        info!("  - allroms.bin: {}", code_path.display());
        Ok(())
    }

    /// Check if there are any ZIP files in the ROM directory
    fn check_for_zip_files(&self) -> bool {
        if let Ok(entries) = fs::read_dir(&self.rom_directory) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.extension().and_then(|s| s.to_str()) == Some("zip") {
                    return true;
                }
            }
        }
        false
    }
    
    /// Validate ROM integrity
    pub fn validate_roms(&self, roms: &LoadedRoms) -> Result<(), RomLoadError> {
        info!("Validating ROM integrity...");
        
        // Basic validation - check for known patterns or headers
        if !self.validate_graphics_rom(&roms.graphics_rom) {
            error!("Graphics ROM validation failed");
            return Err(RomLoadError::UnsupportedVersion {
                version: "Unknown graphics ROM".to_string(),
            });
        }
        
        if !self.validate_code_rom(&roms.code_rom) {
            error!("Code ROM validation failed");
            return Err(RomLoadError::UnsupportedVersion {
                version: "Unknown code ROM".to_string(),
            });
        }
        
        info!("ROM validation successful");
        Ok(())
    }
    
    /// Validate graphics ROM by checking for known patterns
    fn validate_graphics_rom(&self, data: &[u8]) -> bool {
        // Check for Capcom signature or known tile patterns
        // This is a placeholder - actual validation would check for specific patterns
        data.len() > 1024 && !data.iter().all(|&b| b == 0)
    }
    
    /// Validate code ROM by checking for M68k code patterns
    fn validate_code_rom(&self, data: &[u8]) -> bool {
        // Check for M68k reset vector or known code patterns
        // This is a placeholder - actual validation would check for specific patterns
        data.len() > 1024 && !data.iter().all(|&b| b == 0)
    }
}

/// Container for all loaded ROM data
#[derive(Debug, Clone)]
pub struct LoadedRoms {
    pub graphics_rom: Vec<u8>,
    pub code_rom: Vec<u8>,
    pub audio_rom: Option<Vec<u8>>,
    pub sound_rom: Option<Vec<u8>>,
}

impl LoadedRoms {
    /// Get total size of all loaded ROMs
    pub fn total_size(&self) -> usize {
        self.graphics_rom.len()
            + self.code_rom.len()
            + self.audio_rom.as_ref().map_or(0, |data| data.len())
            + self.sound_rom.as_ref().map_or(0, |data| data.len())
    }

    /// Get ROM statistics
    pub fn get_stats(&self) -> RomStats {
        RomStats {
            graphics_size: self.graphics_rom.len(),
            code_size: self.code_rom.len(),
            audio_size: self.audio_rom.as_ref().map_or(0, |data| data.len()),
            sound_size: self.sound_rom.as_ref().map_or(0, |data| data.len()),
            total_size: self.total_size(),
        }
    }
}

/// ROM loading statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RomStats {
    pub graphics_size: usize,
    pub code_size: usize,
    pub audio_size: usize,
    pub sound_size: usize,
    pub total_size: usize,
}

/// Calculate a simple checksum for ROM validation
fn calculate_checksum(data: &[u8]) -> u32 {
    // Simple CRC32-like checksum
    let mut checksum = 0u32;
    for &byte in data {
        checksum = checksum.wrapping_mul(31).wrapping_add(byte as u32);
    }
    checksum
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::io::Write;
    use tempfile::TempDir;
    
    #[test]
    fn test_checksum_calculation() {
        let data = b"Hello, World!";
        let checksum1 = calculate_checksum(data);
        let checksum2 = calculate_checksum(data);
        assert_eq!(checksum1, checksum2);
        
        let different_data = b"Hello, World?";
        let checksum3 = calculate_checksum(different_data);
        assert_ne!(checksum1, checksum3);
    }
    
    #[test]
    fn test_rom_loader_file_not_found() {
        let temp_dir = TempDir::new().unwrap();
        let loader = RomLoader::new(temp_dir.path());
        
        let result = loader.load_all_roms();
        assert!(matches!(result, Err(RomLoadError::FileNotFound { .. })));
    }
}
