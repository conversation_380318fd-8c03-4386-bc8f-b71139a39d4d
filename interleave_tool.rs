#!/usr/bin/env rust-script
//! ```cargo
//! [dependencies]
//! ```

use std::env;
use std::fs::File;
use std::io::{Read, Write, BufWriter};
use std::path::Path;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 4 {
        eprintln!("Usage: {} <output_file> <step_size> <input_file1> [input_file2] ...", args[0]);
        eprintln!("Example: {} output.bin 1 file1.bin file2.bin", args[0]);
        std::process::exit(1);
    }
    
    let output_file = &args[1];
    let step_size: usize = args[2].parse()
        .map_err(|_| "Step size must be a positive integer")?;
    let input_files = &args[3..];
    
    println!("Interleaving {} files with step size {} into {}", 
             input_files.len(), step_size, output_file);
    
    // Read all input files
    let mut file_data = Vec::new();
    for filename in input_files {
        let mut file = File::open(filename)?;
        let mut data = Vec::new();
        file.read_to_end(&mut data)?;
        file_data.push(data);
        println!("Read {}: {} bytes", filename, file_data.last().unwrap().len());
    }
    
    // Find the maximum file size
    let max_size = file_data.iter().map(|data| data.len()).max().unwrap_or(0);
    
    // Create output file
    let output = File::create(output_file)?;
    let mut writer = BufWriter::new(output);
    
    // Interleave the data
    let mut total_written = 0;
    for offset in (0..max_size).step_by(step_size) {
        for data in &file_data {
            let end = std::cmp::min(offset + step_size, data.len());
            if offset < data.len() {
                let chunk = &data[offset..end];
                writer.write_all(chunk)?;
                total_written += chunk.len();
            }
        }
    }
    
    writer.flush()?;
    println!("Created {}: {} bytes", output_file, total_written);
    
    Ok(())
}
