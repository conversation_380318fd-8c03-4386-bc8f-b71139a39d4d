#!/bin/bash
# Proper ROM merging script for SF2 Rust port
# Based on mt2-merge.sh but adapted for our ROM set

set -e  # Exit on any error

echo "Creating proper SF2 ROM files using interleave tool..."

# Change to extracted ROMs directory
cd extracted_roms

# Clean up any existing intermediate files
rm -f int1 int2 int3 int4 gint1 gint2 gint3

echo "Creating code ROM (allroms.bin)..."

# For code ROMs, we need to map our files to the expected pattern
# Original script expects: sf2u.30a sf2u.37a sf2u.31a sf2u.38a sf2u.28a sf2u.35a sf2_29a.bin sf2_36a.bin
# We have: sf2e_30g.11e sf2e_37g.11f sf2e_31g.12e sf2e_38g.12f sf2e_28g.9e sf2e_35g.9f sf2_29b.10e sf2_36b.10f

../interleave int1 1 sf2e_30g.11e sf2e_37g.11f
../interleave int2 1 sf2e_31g.12e sf2e_38g.12f  
../interleave int3 1 sf2e_28g.9e sf2e_35g.9f
../interleave int4 1 sf2_29b.10e sf2_36b.10f

# Concatenate code ROM parts
cat int1 int2 int3 int4 > ../allroms.bin
echo "Created allroms.bin: $(wc -c < ../allroms.bin) bytes"

echo "Creating graphics ROM (sf2gfx.bin)..."

# For graphics ROMs, we have the exact files the script expects
../interleave gint1 2 sf2-5m.4a sf2-7m.6a sf2-1m.3a sf2-3m.5a
../interleave gint2 2 sf2-6m.4c sf2-8m.6c sf2-2m.3c sf2-4m.5c
../interleave gint3 2 sf2-13m.4d sf2-15m.6d sf2-9m.3d sf2-11m.5d

# Concatenate graphics ROM parts
cat gint1 gint2 gint3 > ../sf2gfx.bin
echo "Created sf2gfx.bin: $(wc -c < ../sf2gfx.bin) bytes"

# Clean up intermediate files
rm -f int1 int2 int3 int4 gint1 gint2 gint3

cd ..

echo ""
echo "ROM creation complete!"
echo "Files created:"
echo "  allroms.bin: $(wc -c < allroms.bin) bytes"
echo "  sf2gfx.bin: $(wc -c < sf2gfx.bin) bytes"
echo ""
echo "Checking file sizes against expected values..."
echo "Expected sf2gfx.bin: ~6MB (6291456 bytes)"
echo "Expected allroms.bin: ~1MB (1048576 bytes)"

# Calculate checksums
echo ""
echo "Calculating checksums..."
echo "allroms.bin:"
shasum allroms.bin
echo "Expected: 4256ec60bf9eec21f4d6bb34c38990a9401af82e"
echo ""
echo "sf2gfx.bin:"
shasum sf2gfx.bin
echo "Expected: db52a6314b4c0cd4c48eb324720c83dd142c3bff"
