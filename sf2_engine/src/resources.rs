//! # Resources
//! 
//! Global resources for the Street Fighter II game engine.

use bevy::prelude::*;
use crate::components::*;

/// Global game configuration
#[derive(Resource)]
pub struct GameConfig {
    pub screen_width: u32,
    pub screen_height: u32,
    pub target_fps: u32,
    pub difficulty: u8,
    pub sound_enabled: bool,
    pub music_enabled: bool,
}

impl Default for GameConfig {
    fn default() -> Self {
        Self {
            screen_width: 1024,
            screen_height: 768,
            target_fps: 60,
            difficulty: 4, // Medium difficulty
            sound_enabled: true,
            music_enabled: true,
        }
    }
}

/// Global input buffer resource
#[derive(Resource, Default)]
pub struct InputBuffer {
    pub player1_buffer: Vec<InputEvent>,
    pub player2_buffer: Vec<InputEvent>,
    pub max_buffer_size: usize,
}

impl InputBuffer {
    pub fn new() -> Self {
        Self {
            player1_buffer: Vec::new(),
            player2_buffer: Vec::new(),
            max_buffer_size: 60, // 1 second at 60 FPS
        }
    }
}

/// Round timer resource
#[derive(Resource)]
pub struct RoundTimer {
    pub time_remaining: f32,
    pub total_time: f32,
    pub is_paused: bool,
}

impl Default for RoundTimer {
    fn default() -> Self {
        Self {
            time_remaining: 99.0,
            total_time: 99.0,
            is_paused: false,
        }
    }
}

/// Score tracking resource
#[derive(Resource, Default)]
pub struct ScoreTracker {
    pub player1_score: u32,
    pub player2_score: u32,
    pub player1_rounds_won: u8,
    pub player2_rounds_won: u8,
}

/// Debug information resource
#[derive(Resource, Default)]
pub struct DebugInfo {
    pub show_hitboxes: bool,
    pub show_fps: bool,
    pub show_input_buffer: bool,
    pub frame_count: u64,
}

/// Performance metrics resource
#[derive(Resource, Default)]
pub struct PerformanceMetrics {
    pub fps: f32,
    pub frame_time_ms: f32,
    pub entities_count: usize,
    pub systems_time_ms: f32,
}
