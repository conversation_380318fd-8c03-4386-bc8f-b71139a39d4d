//! # SF2 Audio Playbook System
//! 
//! Connects extracted SF2 ROM audio samples with game actions and events
//! to provide authentic Street Fighter II audio experience.

use bevy::prelude::*;
use std::collections::HashMap;
use log::{info, debug};

use crate::components::*;
use crate::events::*;
use crate::states::*;
use sf2_assets::{SF2AudioEvent, ExtractedAudio};
use sf2_types::{FighterId, AttackStrength, SpecialMoveId};

/// SF2 Audio Playbook - maps game events to specific SF2 audio samples
#[derive(Resource)]
pub struct SF2AudioPlaybook {
    /// Character voice samples (indexed by character and action)
    pub character_voices: HashMap<FighterId, HashMap<String, String>>,
    /// Sound effects for game actions
    pub sfx_mapping: HashMap<String, String>,
    /// Background music tracks
    pub music_tracks: HashMap<String, String>,
    /// Audio sample metadata for validation
    pub available_samples: Vec<String>,
}

impl Default for SF2AudioPlaybook {
    fn default() -> Self {
        let mut playbook = Self {
            character_voices: HashMap::new(),
            sfx_mapping: HashMap::new(),
            music_tracks: HashMap::new(),
            available_samples: Vec::new(),
        };
        
        playbook.initialize_sf2_audio_mapping();
        playbook
    }
}

impl SF2AudioPlaybook {
    /// Initialize authentic SF2 audio mappings based on original game
    fn initialize_sf2_audio_mapping(&mut self) {
        // Character voice samples - based on original SF2 audio layout
        self.setup_character_voices();
        
        // Sound effects mapping
        self.setup_sfx_mapping();
        
        // Background music tracks
        self.setup_music_tracks();
        
        info!("SF2 Audio Playbook initialized with {} character voice sets, {} SFX, {} music tracks",
              self.character_voices.len(), self.sfx_mapping.len(), self.music_tracks.len());
    }
    
    fn setup_character_voices(&mut self) {
        // Ryu voice samples
        let mut ryu_voices = HashMap::new();
        ryu_voices.insert("hadoken".to_string(), "ryu_hadoken".to_string());
        ryu_voices.insert("shoryuken".to_string(), "ryu_shoryuken".to_string());
        ryu_voices.insert("tatsumaki".to_string(), "ryu_tatsumaki".to_string());
        ryu_voices.insert("hit_light".to_string(), "ryu_hit_light".to_string());
        ryu_voices.insert("hit_heavy".to_string(), "ryu_hit_heavy".to_string());
        ryu_voices.insert("ko".to_string(), "ryu_ko".to_string());
        self.character_voices.insert(FighterId::Ryu, ryu_voices);
        
        // Ken voice samples
        let mut ken_voices = HashMap::new();
        ken_voices.insert("hadoken".to_string(), "ken_hadoken".to_string());
        ken_voices.insert("shoryuken".to_string(), "ken_shoryuken".to_string());
        ken_voices.insert("tatsumaki".to_string(), "ken_tatsumaki".to_string());
        ken_voices.insert("hit_light".to_string(), "ken_hit_light".to_string());
        ken_voices.insert("hit_heavy".to_string(), "ken_hit_heavy".to_string());
        ken_voices.insert("ko".to_string(), "ken_ko".to_string());
        self.character_voices.insert(FighterId::Ken, ken_voices);
        
        // Chun-Li voice samples
        let mut chunli_voices = HashMap::new();
        chunli_voices.insert("kikoken".to_string(), "chunli_kikoken".to_string());
        chunli_voices.insert("spinning_bird_kick".to_string(), "chunli_spinning_bird".to_string());
        chunli_voices.insert("lightning_legs".to_string(), "chunli_lightning_legs".to_string());
        chunli_voices.insert("hit_light".to_string(), "chunli_hit_light".to_string());
        chunli_voices.insert("hit_heavy".to_string(), "chunli_hit_heavy".to_string());
        chunli_voices.insert("ko".to_string(), "chunli_ko".to_string());
        self.character_voices.insert(FighterId::ChunLi, chunli_voices);
        
        // Add more characters as needed...
    }
    
    fn setup_sfx_mapping(&mut self) {
        // Combat sound effects
        self.sfx_mapping.insert("punch_light".to_string(), "sfx_punch_light".to_string());
        self.sfx_mapping.insert("punch_medium".to_string(), "sfx_punch_medium".to_string());
        self.sfx_mapping.insert("punch_heavy".to_string(), "sfx_punch_heavy".to_string());
        self.sfx_mapping.insert("kick_light".to_string(), "sfx_kick_light".to_string());
        self.sfx_mapping.insert("kick_medium".to_string(), "sfx_kick_medium".to_string());
        self.sfx_mapping.insert("kick_heavy".to_string(), "sfx_kick_heavy".to_string());
        
        // Special move effects
        self.sfx_mapping.insert("fireball".to_string(), "sfx_fireball".to_string());
        self.sfx_mapping.insert("uppercut".to_string(), "sfx_uppercut".to_string());
        self.sfx_mapping.insert("hurricane_kick".to_string(), "sfx_hurricane".to_string());
        
        // Game state effects
        self.sfx_mapping.insert("round_start".to_string(), "sfx_round_start".to_string());
        self.sfx_mapping.insert("round_end".to_string(), "sfx_round_end".to_string());
        self.sfx_mapping.insert("perfect".to_string(), "sfx_perfect".to_string());
        self.sfx_mapping.insert("ko".to_string(), "sfx_ko".to_string());
        
        // UI sound effects
        self.sfx_mapping.insert("menu_select".to_string(), "sfx_menu_select".to_string());
        self.sfx_mapping.insert("menu_confirm".to_string(), "sfx_menu_confirm".to_string());
        self.sfx_mapping.insert("menu_cancel".to_string(), "sfx_menu_cancel".to_string());
    }
    
    fn setup_music_tracks(&mut self) {
        // Character stage themes
        self.music_tracks.insert("ryu_stage".to_string(), "music_ryu_stage".to_string());
        self.music_tracks.insert("ken_stage".to_string(), "music_ken_stage".to_string());
        self.music_tracks.insert("chunli_stage".to_string(), "music_chunli_stage".to_string());
        self.music_tracks.insert("blanka_stage".to_string(), "music_blanka_stage".to_string());
        self.music_tracks.insert("zangief_stage".to_string(), "music_zangief_stage".to_string());
        self.music_tracks.insert("dhalsim_stage".to_string(), "music_dhalsim_stage".to_string());
        self.music_tracks.insert("ehonda_stage".to_string(), "music_ehonda_stage".to_string());
        self.music_tracks.insert("guile_stage".to_string(), "music_guile_stage".to_string());
        
        // Boss stage themes
        self.music_tracks.insert("balrog_stage".to_string(), "music_balrog_stage".to_string());
        self.music_tracks.insert("vega_stage".to_string(), "music_vega_stage".to_string());
        self.music_tracks.insert("sagat_stage".to_string(), "music_sagat_stage".to_string());
        self.music_tracks.insert("bison_stage".to_string(), "music_bison_stage".to_string());
        
        // Menu and special themes
        self.music_tracks.insert("title_screen".to_string(), "music_title".to_string());
        self.music_tracks.insert("character_select".to_string(), "music_character_select".to_string());
        self.music_tracks.insert("victory".to_string(), "music_victory".to_string());
        self.music_tracks.insert("game_over".to_string(), "music_game_over".to_string());
    }
    
    /// Get character voice sample name for a specific action
    pub fn get_character_voice(&self, fighter_id: FighterId, action: &str) -> Option<&String> {
        self.character_voices.get(&fighter_id)?.get(action)
    }
    
    /// Get SFX sample name for a game action
    pub fn get_sfx(&self, action: &str) -> Option<&String> {
        self.sfx_mapping.get(action)
    }
    
    /// Get music track name for a stage or game state
    pub fn get_music_track(&self, track_key: &str) -> Option<&String> {
        self.music_tracks.get(track_key)
    }
    
    /// Update available samples list from extracted audio
    pub fn update_available_samples(&mut self, extracted_audio: &ExtractedAudio) {
        self.available_samples.clear();
        for (_id, sample) in &extracted_audio.samples {
            self.available_samples.push(sample.metadata.name.clone());
        }
        info!("Updated SF2 Audio Playbook with {} available samples", self.available_samples.len());
    }
    
    /// Check if a sample is available
    pub fn is_sample_available(&self, sample_name: &str) -> bool {
        self.available_samples.contains(&sample_name.to_string())
    }
}

/// SF2 Audio Playbook Plugin
pub struct SF2AudioPlaybookPlugin;

impl Plugin for SF2AudioPlaybookPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<SF2AudioPlaybook>()
            .add_systems(Update, (
                handle_character_audio_events,
                handle_combat_audio_events,
                handle_game_state_audio_events,
                update_playbook_samples,
            ));
    }
}

/// System to handle character-specific audio events
fn handle_character_audio_events(
    mut audio_events: EventWriter<SF2AudioEvent>,
    mut special_move_events: EventReader<SpecialMoveEvent>,
    playbook: Res<SF2AudioPlaybook>,
    fighter_query: Query<&Fighter>,
) {
    for event in special_move_events.read() {
        if let Ok(fighter) = fighter_query.get(event.entity) {
            // Extract special move ID from the pattern
            let action_name = match event.pattern {
                sf2_types::SpecialMovePattern::QuarterCircleForward(_) => "hadoken",
                sf2_types::SpecialMovePattern::DragonPunch(_) => "shoryuken",
                sf2_types::SpecialMovePattern::QuarterCircleBack(_) => "tatsumaki",
                sf2_types::SpecialMovePattern::ChargeBackForward(_) => "sonic_boom",
                sf2_types::SpecialMovePattern::ChargeDownUp(_) => "flash_kick",
                _ => continue,
            };

            if let Some(sample_name) = playbook.get_character_voice(fighter.fighter_id, action_name) {
                if playbook.is_sample_available(sample_name) {
                    audio_events.send(SF2AudioEvent::PlaySFX {
                        sample_name: sample_name.clone(),
                        volume: Some(1.0),
                        position: None,
                    });
                    debug!("Playing character voice: {} for {}", sample_name, action_name);
                }
            }
        }
    }
}

/// System to handle combat audio events
fn handle_combat_audio_events(
    mut audio_events: EventWriter<SF2AudioEvent>,
    mut hit_events: EventReader<HitEvent>,
    playbook: Res<SF2AudioPlaybook>,
) {
    for event in hit_events.read() {
        // For now, use a generic hit sound since HitEvent doesn't have attack_strength
        // This could be enhanced by adding attack strength to HitEvent
        let sfx_key = "punch_medium"; // Default to medium punch sound

        if let Some(sample_name) = playbook.get_sfx(sfx_key) {
            if playbook.is_sample_available(sample_name) {
                audio_events.send(SF2AudioEvent::PlaySFX {
                    sample_name: sample_name.clone(),
                    volume: Some(0.8),
                    position: Some(bevy::math::Vec3::new(
                        event.hit_position.x as f32,
                        event.hit_position.y as f32,
                        0.0
                    )),
                });
                debug!("Playing combat SFX: {} for hit", sample_name);
            }
        }
    }
}

/// System to handle game state audio events
fn handle_game_state_audio_events(
    mut audio_events: EventWriter<SF2AudioEvent>,
    game_state: Res<State<GameState>>,
    fight_state: Res<State<FightState>>,
    playbook: Res<SF2AudioPlaybook>,
) {
    // Handle game state changes for music
    if game_state.is_changed() {
        let music_key = match game_state.get() {
            GameState::MainMenu => "title_screen",
            GameState::CharacterSelect => "character_select",
            GameState::InGame => return, // Handle in fight state
            GameState::GameOver => "game_over",
            _ => return,
        };

        if let Some(track_name) = playbook.get_music_track(music_key) {
            if playbook.is_sample_available(track_name) {
                audio_events.send(SF2AudioEvent::PlayMusic {
                    track_name: track_name.clone(),
                    loop_track: true,
                    fade_in_duration: Some(1.0),
                });
                debug!("Playing background music: {}", track_name);
            }
        }
    }

    // Handle fight state changes
    if fight_state.is_changed() {
        match fight_state.get() {
            FightState::PreFight => {
                if let Some(sample_name) = playbook.get_sfx("round_start") {
                    if playbook.is_sample_available(sample_name) {
                        audio_events.send(SF2AudioEvent::PlaySFX {
                            sample_name: sample_name.clone(),
                            volume: Some(1.0),
                            position: None,
                        });
                    }
                }
            },
            FightState::RoundEnd => {
                if let Some(sample_name) = playbook.get_sfx("round_end") {
                    if playbook.is_sample_available(sample_name) {
                        audio_events.send(SF2AudioEvent::PlaySFX {
                            sample_name: sample_name.clone(),
                            volume: Some(1.0),
                            position: None,
                        });
                    }
                }
            },
            _ => {}
        }
    }
}

/// System to update playbook with available samples
fn update_playbook_samples(
    mut playbook: ResMut<SF2AudioPlaybook>,
    extracted_audio: Res<ExtractedAudio>,
) {
    if extracted_audio.is_changed() {
        playbook.update_available_samples(&extracted_audio);
    }
}
