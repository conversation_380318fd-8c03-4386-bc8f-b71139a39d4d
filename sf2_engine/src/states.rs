//! # States
//! 
//! Game state definitions for the Street Fighter II game engine.

use bevy::prelude::*;

/// Main game state
#[derive(States, Debug, Clone, Copy, Eq, PartialEq, Hash, Default)]
pub enum GameState {
    #[default]
    Loading,
    MainMenu,
    CharacterSelect,
    InGame,
    Paused,
    GameOver,
    Credits,
}

/// Fight-specific state (sub-state of InGame)
#[derive(States, Debug, <PERSON>lone, Copy, Eq, PartialEq, <PERSON>h, Default)]
pub enum FightState {
    #[default]
    PreFight,
    Fighting,
    RoundEnd,
    Victory,
}

/// Menu state for navigation
#[derive(States, Debug, Clone, Copy, Eq, PartialEq, Hash, Default)]
pub enum MenuState {
    #[default]
    Main,
    Options,
    Controls,
    Audio,
    Graphics,
}

/// Character selection state
#[derive(States, Debug, Clone, Copy, Eq, PartialEq, Hash, Default)]
pub enum CharacterSelectState {
    #[default]
    Player1Selecting,
    Player2Selecting,
    StageSelect,
    Confirmed,
}
