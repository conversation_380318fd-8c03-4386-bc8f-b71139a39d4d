//! # SF2 Sprite Graphics System
//!
//! Manages authentic Street Fighter II sprite rendering using extracted ROM data.

use bevy::prelude::*;
use sf2_assets::{ExtractedSprites, SpriteExtractorResource, SF2AudioEvent};
use sf2_types::{FighterId, FighterStateData, FighterState as SF2FighterState, FighterSubState, FighterStance};
use crate::components::{Fighter, AnimationState, FighterState, Stance, Velocity, Position};
use crate::audio_playbook::SF2AudioPlaybook;
use std::collections::HashMap;

/// Resource for managing SF2 character sprite textures
#[derive(Resource, Default)]
pub struct SF2SpriteManager {
    /// Character sprite textures indexed by fighter ID and animation frame
    pub character_textures: HashMap<FighterId, HashMap<String, Vec<Handle<Image>>>>,
    /// Loaded sprite metadata
    pub sprite_metadata: HashMap<u32, SF2SpriteMetadata>,
    /// Default fallback texture for missing sprites
    pub fallback_texture: Option<Handle<Image>>,
}

/// Metadata for SF2 sprites
#[derive(Debug, Clone)]
pub struct SF2SpriteMetadata {
    pub sprite_id: u32,
    pub fighter_id: FighterId,
    pub animation_name: String,
    pub frame_index: usize,
    pub width: u16,
    pub height: u16,
    pub offset_x: i16,
    pub offset_y: i16,
}

/// Component for SF2 character sprite rendering
#[derive(Component)]
pub struct SF2CharacterSprite {
    pub fighter_id: FighterId,
    pub current_animation: String,
    pub current_frame: usize,
    pub sprite_scale: Vec2,
    pub flip_x: bool,
}

/// Component for advanced animation control
#[derive(Component)]
pub struct SF2AnimationController {
    pub fighter_id: FighterId,
    pub current_animation: String,
    pub current_frame: usize,
    pub frame_timer: f32,
    pub animation_speed: f32, // Multiplier for animation speed
    pub loop_current: bool,
    pub next_animation: Option<String>, // Queue next animation
    pub animation_locked: bool, // Prevent animation changes during certain states
    pub frame_events: Vec<AnimationFrameEvent>, // Events to trigger on specific frames
}

/// Events that can be triggered on specific animation frames
#[derive(Debug, Clone)]
pub struct AnimationFrameEvent {
    pub frame_number: usize,
    pub event_type: AnimationEventType,
    pub triggered: bool,
}

/// Types of events that can occur during animation
#[derive(Debug, Clone)]
pub enum AnimationEventType {
    PlaySound(String),
    SpawnEffect(String),
    EnableHitbox,
    DisableHitbox,
    AllowStateChange,
    TriggerSpecialMove,
}

/// Component to hold the texture handle for SF2 sprites
#[derive(Component)]
pub struct SF2TextureHandle(pub Handle<Image>);

impl Default for SF2CharacterSprite {
    fn default() -> Self {
        Self {
            fighter_id: FighterId::Ryu,
            current_animation: "idle".to_string(),
            current_frame: 0,
            sprite_scale: Vec2::new(2.0, 2.0), // 2x scale for better visibility
            flip_x: false,
        }
    }
}

impl Default for SF2AnimationController {
    fn default() -> Self {
        Self {
            fighter_id: FighterId::Ryu,
            current_animation: "idle".to_string(),
            current_frame: 0,
            frame_timer: 0.0,
            animation_speed: 1.0,
            loop_current: true,
            next_animation: None,
            animation_locked: false,
            frame_events: Vec::new(),
        }
    }
}

impl SF2AnimationController {
    /// Create new animation controller for a specific fighter
    pub fn new(fighter_id: FighterId) -> Self {
        Self {
            fighter_id,
            current_animation: "idle".to_string(),
            current_frame: 0,
            frame_timer: 0.0,
            animation_speed: 1.0,
            loop_current: true,
            next_animation: None,
            animation_locked: false,
            frame_events: Vec::new(),
        }
    }

    /// Change animation if not locked
    pub fn change_animation(&mut self, animation_name: &str, force: bool) -> bool {
        if self.animation_locked && !force {
            // Queue the animation for later
            self.next_animation = Some(animation_name.to_string());
            return false;
        }

        if self.current_animation != animation_name || force {
            self.current_animation = animation_name.to_string();
            self.current_frame = 0;
            self.frame_timer = 0.0;
            self.reset_frame_events();
            return true;
        }
        false
    }

    /// Lock animation to prevent changes
    pub fn lock_animation(&mut self) {
        self.animation_locked = true;
    }

    /// Unlock animation and apply queued animation if any
    pub fn unlock_animation(&mut self) -> Option<String> {
        self.animation_locked = false;
        if let Some(next_anim) = self.next_animation.take() {
            self.change_animation(&next_anim, true);
            Some(next_anim)
        } else {
            None
        }
    }

    /// Reset frame events for new animation
    fn reset_frame_events(&mut self) {
        for event in &mut self.frame_events {
            event.triggered = false;
        }
    }

    /// Add frame event
    pub fn add_frame_event(&mut self, frame: usize, event_type: AnimationEventType) {
        self.frame_events.push(AnimationFrameEvent {
            frame_number: frame,
            event_type,
            triggered: false,
        });
    }
}

/// SF2 sprite animation definitions
#[derive(Resource)]
pub struct SF2AnimationDefinitions {
    pub animations: HashMap<FighterId, HashMap<String, SF2Animation>>,
}

/// Animation definition for SF2 characters
#[derive(Debug, Clone)]
pub struct SF2Animation {
    pub name: String,
    pub frames: Vec<SF2AnimationFrame>,
    pub loop_animation: bool,
    pub frame_duration: f32, // Duration per frame in seconds
}

/// Individual animation frame
#[derive(Debug, Clone)]
pub struct SF2AnimationFrame {
    pub sprite_id: u32,
    pub duration_override: Option<f32>, // Override default frame duration
    pub offset: Vec2, // Additional offset for this frame
}

impl Default for SF2AnimationDefinitions {
    fn default() -> Self {
        let mut animations = HashMap::new();

        // Initialize comprehensive animations for Ryu
        let mut ryu_animations = HashMap::new();

        // Ryu Idle Animation
        ryu_animations.insert("idle".to_string(), SF2Animation {
            name: "idle".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 0, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 1, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 2, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 3, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.25, // Slower idle animation
        });

        // Ryu Walking Forward
        ryu_animations.insert("walk_forward".to_string(), SF2Animation {
            name: "walk_forward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 16, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 17, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 18, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 19, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 20, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 21, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.08, // Fast walking animation
        });

        // Ryu Walking Backward
        ryu_animations.insert("walk_backward".to_string(), SF2Animation {
            name: "walk_backward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 22, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 23, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 24, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 25, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.1,
        });

        // Ryu Crouching
        ryu_animations.insert("crouch".to_string(), SF2Animation {
            name: "crouch".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 32, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: false,
            frame_duration: 0.1,
        });

        // Ryu Light Punch
        ryu_animations.insert("light_punch".to_string(), SF2Animation {
            name: "light_punch".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 48, duration_override: Some(0.05), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 49, duration_override: Some(0.1), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 50, duration_override: Some(0.05), offset: Vec2::ZERO },
            ],
            loop_animation: false,
            frame_duration: 0.08,
        });

        // Ryu Medium Punch
        ryu_animations.insert("medium_punch".to_string(), SF2Animation {
            name: "medium_punch".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 51, duration_override: Some(0.08), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 52, duration_override: Some(0.12), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 53, duration_override: Some(0.08), offset: Vec2::ZERO },
            ],
            loop_animation: false,
            frame_duration: 0.1,
        });

        animations.insert(FighterId::Ryu, ryu_animations);

        // Initialize comprehensive animations for Ken
        let mut ken_animations = HashMap::new();

        // Ken Idle Animation
        ken_animations.insert("idle".to_string(), SF2Animation {
            name: "idle".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 64, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 65, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 66, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 67, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.22, // Slightly different timing than Ryu
        });

        // Ken Walking Forward
        ken_animations.insert("walk_forward".to_string(), SF2Animation {
            name: "walk_forward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 80, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 81, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 82, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 83, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 84, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 85, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.075, // Ken walks slightly faster
        });

        // Ken Walking Backward
        ken_animations.insert("walk_backward".to_string(), SF2Animation {
            name: "walk_backward".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 86, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 87, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 88, duration_override: None, offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 89, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: true,
            frame_duration: 0.09,
        });

        // Ken Crouching
        ken_animations.insert("crouch".to_string(), SF2Animation {
            name: "crouch".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 96, duration_override: None, offset: Vec2::ZERO },
            ],
            loop_animation: false,
            frame_duration: 0.1,
        });

        // Ken Light Punch
        ken_animations.insert("light_punch".to_string(), SF2Animation {
            name: "light_punch".to_string(),
            frames: vec![
                SF2AnimationFrame { sprite_id: 112, duration_override: Some(0.04), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 113, duration_override: Some(0.09), offset: Vec2::ZERO },
                SF2AnimationFrame { sprite_id: 114, duration_override: Some(0.04), offset: Vec2::ZERO },
            ],
            loop_animation: false,
            frame_duration: 0.07, // Ken punches slightly faster
        });

        animations.insert(FighterId::Ken, ken_animations);

        Self { animations }
    }
}

/// Plugin for SF2 sprite graphics system
pub struct SF2SpriteGraphicsPlugin;

impl Plugin for SF2SpriteGraphicsPlugin {
    fn build(&self, app: &mut App) {
        app
            .init_resource::<SF2SpriteManager>()
            .init_resource::<SF2AnimationDefinitions>()
            .add_systems(Update, (
                load_character_sprites_system,
                initialize_animation_controllers_system,
                update_fighter_animation_system,
                update_character_sprite_system,
                animate_character_sprites_system,
            )); // Systems will run in order
    }
}

/// System to load character sprites from extracted ROM data
fn load_character_sprites_system(
    mut sprite_manager: ResMut<SF2SpriteManager>,
    extracted_sprites: Res<ExtractedSprites>,
    sprite_extractor_res: Res<SpriteExtractorResource>,
    mut images: ResMut<Assets<Image>>,
    mut commands: Commands,
) {
    // Check if we already loaded sprites
    if !sprite_manager.character_textures.is_empty() {
        // Already loaded sprites
        debug!("🎮 Sprites already loaded, skipping sprite loading system");
        return;
    }

    // Add debug logging to track system execution
    debug!("🔄 Sprite loading system running - checking extractor availability...");
    debug!("🔍 Sprite extractor available: {}", sprite_extractor_res.extractor.is_some());
    debug!("🔍 Character textures loaded: {}", !sprite_manager.character_textures.is_empty());

    // Check if we have a sprite extractor available
    if sprite_extractor_res.extractor.is_none() {
        // Add debug logging to understand why extractor is not available
        debug!("🔍 Sprite extractor not yet available, waiting...");
        return;
    }

    info!("🎯 Sprite extractor is now available! Starting sprite loading...");

    if let Some(ref extractor) = sprite_extractor_res.extractor {
        info!("🎮 Loading SF2 character sprites from extracted ROM data");
        info!("📊 Sprite extractor available with {} total sprites", extractor.get_total_sprite_count());
        
        // Load comprehensive sprites for Ryu and Ken animations
        let sprite_ids_to_load = vec![
            // Ryu sprites (0-31)
            0, 1, 2, 3, 16, 17, 18, 19, 20, 21,
            // Ken sprites (64-127) - Complete set for all animations
            64, 65, 66, 67,     // Ken idle (sprites 0-3)
            80, 81, 82, 83, 84, 85,  // Ken walk forward (sprites 4-9)
            86, 87, 88,         // Ken walk backward (sprites 10-12)
            96,                 // Ken crouch (sprite 13)
            112, 113, 114,      // Ken light punch (sprites 14-16)
        ];
        
        for sprite_id in sprite_ids_to_load {
            match extractor.extract_sprite(sprite_id) {
                Ok(extracted_sprite) => {
                    // Create Bevy image handle
                    let image_handle = images.add(extracted_sprite.bevy_image);
                    
                    // Determine fighter ID based on sprite ID ranges
                    let fighter_id = if sprite_id < 64 {
                        FighterId::Ryu
                    } else if sprite_id < 128 {
                        FighterId::Ken
                    } else {
                        continue; // Skip other characters for now
                    };
                    
                    // Store sprite metadata
                    sprite_manager.sprite_metadata.insert(sprite_id, SF2SpriteMetadata {
                        sprite_id,
                        fighter_id,
                        animation_name: if sprite_id % 64 < 16 { "idle" } else { "walk_forward" }.to_string(),
                        frame_index: (sprite_id % 16) as usize,
                        width: extracted_sprite.metadata.width,
                        height: extracted_sprite.metadata.height,
                        offset_x: extracted_sprite.metadata.offset_x,
                        offset_y: extracted_sprite.metadata.offset_y,
                    });
                    
                    // Store texture handle
                    sprite_manager.character_textures
                        .entry(fighter_id)
                        .or_insert_with(HashMap::new)
                        .entry("sprites".to_string())
                        .or_insert_with(Vec::new)
                        .push(image_handle);
                    
                    info!("✅ Loaded sprite {} for {:?} ({}x{}) - stored at index {}",
                          sprite_id, fighter_id, extracted_sprite.metadata.width, extracted_sprite.metadata.height,
                          sprite_manager.character_textures.get(&fighter_id).unwrap().get("sprites").unwrap().len() - 1);
                }
                Err(e) => {
                    warn!("Failed to extract sprite {}: {}", sprite_id, e);
                }
            }
        }
        
        info!("SF2 character sprite loading complete");
    }
}

/// System to update character sprite components
fn update_character_sprite_system(
    mut query: Query<(Entity, &Fighter, &mut SF2CharacterSprite), Added<Fighter>>,
    mut commands: Commands,
) {
    for (entity, fighter, mut sprite_component) in query.iter_mut() {
        // Initialize sprite component for new fighters
        sprite_component.fighter_id = fighter.fighter_id;
        sprite_component.current_animation = "idle".to_string();
        sprite_component.current_frame = 0;
        
        info!("Initialized SF2 sprite component for {:?}", fighter.fighter_id);
    }
}

/// System to determine animation based on fighter state
fn update_fighter_animation_system(
    mut query: Query<(
        &Fighter,
        &FighterState,
        &Velocity,
        &mut SF2AnimationController,
        Option<&FighterStateData>,
    )>,
) {
    for (fighter, fighter_state, velocity, mut anim_controller, state_data) in query.iter_mut() {
        let new_animation = determine_animation_from_state(
            fighter_state,
            velocity,
            state_data,
        );

        // Change animation if different from current
        if new_animation != anim_controller.current_animation {
            anim_controller.change_animation(&new_animation, false);
            info!("Fighter {:?} animation changed to: {}", fighter.fighter_id, new_animation);
        }
    }
}

/// Determine appropriate animation based on fighter state
fn determine_animation_from_state(
    fighter_state: &FighterState,
    velocity: &Velocity,
    state_data: Option<&FighterStateData>,
) -> String {
    // Check if we have detailed state data
    if let Some(state_data) = state_data {
        match state_data.state {
            SF2FighterState::Normal => {
                match state_data.sub_state {
                    FighterSubState::Stand => {
                        if velocity.x.abs() > sf2_types::Fixed16_16::from_f32(0.1) {
                            if velocity.x > sf2_types::Fixed16_16::ZERO {
                                "walk_forward".to_string()
                            } else {
                                "walk_backward".to_string()
                            }
                        } else {
                            "idle".to_string()
                        }
                    }
                    FighterSubState::Walking => {
                        if velocity.x > sf2_types::Fixed16_16::ZERO {
                            "walk_forward".to_string()
                        } else {
                            "walk_backward".to_string()
                        }
                    }
                    FighterSubState::Crouch => "crouch".to_string(),
                    FighterSubState::JumpStart => "jump".to_string(),
                    _ => "idle".to_string(),
                }
            }
            SF2FighterState::Attacking => {
                // For now, use a simple attack animation mapping
                // TODO: Implement proper attack type detection based on input
                "light_punch".to_string()
            }
            SF2FighterState::StandBlock => "block".to_string(),
            SF2FighterState::Reel => "hit".to_string(),
            SF2FighterState::Crouch => "crouch".to_string(),
            SF2FighterState::Jumping => "jump".to_string(),
            _ => "idle".to_string(),
        }
    } else {
        // Fallback to basic state analysis
        match fighter_state.stance {
            Stance::Standing => {
                if velocity.x.abs() > sf2_types::Fixed16_16::from_f32(0.1) {
                    "walk_forward".to_string()
                } else {
                    "idle".to_string()
                }
            }
            Stance::Crouching => "crouch".to_string(),
            Stance::Jumping => "jump".to_string(),
            Stance::Attacking => "light_punch".to_string(),
            Stance::Blocking => "block".to_string(),
            Stance::Stunned => "hit".to_string(),
        }
    }
}

/// Enhanced animation system with frame-based control
fn animate_character_sprites_system(
    mut query: Query<(
        Entity,
        &Fighter,
        &mut SF2CharacterSprite,
        &mut SF2AnimationController,
        &mut AnimationState,
        &mut SF2TextureHandle,
        &mut Sprite,
    )>,
    sprite_manager: Res<SF2SpriteManager>,
    animation_defs: Res<SF2AnimationDefinitions>,
    audio_playbook: Res<SF2AudioPlaybook>,
    mut audio_events: EventWriter<SF2AudioEvent>,
    time: Res<Time>,
) {
    for (entity, fighter, mut sprite_component, mut anim_controller, mut anim_state, mut texture_handle, mut sprite) in query.iter_mut() {
        // Update animation controller timer
        anim_controller.frame_timer += time.delta_secs() * anim_controller.animation_speed;

        // Sync sprite component with controller
        sprite_component.current_animation = anim_controller.current_animation.clone();
        sprite_component.current_frame = anim_controller.current_frame;

        // Get animation definition
        if let Some(fighter_animations) = animation_defs.animations.get(&anim_controller.fighter_id) {
            if let Some(animation) = fighter_animations.get(&anim_controller.current_animation) {
                // Get frame duration (with potential override)
                let frame_duration = if let Some(frame_data) = animation.frames.get(anim_controller.current_frame) {
                    frame_data.duration_override.unwrap_or(animation.frame_duration)
                } else {
                    animation.frame_duration
                };

                // Check if we need to advance frame
                if anim_controller.frame_timer >= frame_duration {
                    anim_controller.frame_timer = 0.0;

                    // Store current frame for event processing
                    let current_frame = anim_controller.current_frame;

                    // Process frame events before advancing
                    process_frame_events(
                        &mut anim_controller,
                        current_frame,
                        fighter.fighter_id,
                        &audio_playbook,
                        &mut audio_events
                    );

                    // Advance frame
                    if anim_controller.current_frame + 1 >= animation.frames.len() {
                        if animation.loop_animation && anim_controller.loop_current {
                            anim_controller.current_frame = 0;
                        } else {
                            // Animation finished, unlock if locked
                            if anim_controller.animation_locked {
                                anim_controller.unlock_animation();
                            }
                        }
                    } else {
                        anim_controller.current_frame += 1;
                    }

                    // Update legacy animation state
                    anim_state.frame = anim_controller.current_frame as u32;
                    anim_state.current_animation = anim_controller.current_animation.clone();
                }

                // Update sprite texture if we have loaded textures
                if let Some(fighter_textures) = sprite_manager.character_textures.get(&anim_controller.fighter_id) {
                    if let Some(sprite_textures) = fighter_textures.get("sprites") {
                        if let Some(frame_data) = animation.frames.get(anim_controller.current_frame) {
                            // Find texture for this sprite ID
                            if let Some(_sprite_metadata) = sprite_manager.sprite_metadata.get(&frame_data.sprite_id) {
                                // CRITICAL FIX: Map sprite ID to actual array index
                                // The sprite_ids_to_load array matches the loading order above
                                let sprite_ids_to_load = vec![
                                    0, 1, 2, 3, 16, 17, 18, 19, 20, 21,  // Ryu sprites (indices 0-9)
                                    64, 65, 66, 67,                      // Ken idle (indices 10-13)
                                    80, 81, 82, 83, 84, 85,              // Ken walk forward (indices 14-19)
                                    86, 87, 88,                          // Ken walk backward (indices 20-22)
                                    96,                                   // Ken crouch (index 23)
                                    112, 113, 114,                       // Ken light punch (indices 24-26)
                                ];

                                if let Some(texture_index) = sprite_ids_to_load.iter().position(|&id| id == frame_data.sprite_id) {
                                    if let Some(sprite_texture) = sprite_textures.get(texture_index) {
                                        // Apply the actual texture to the sprite
                                        texture_handle.0 = sprite_texture.clone();
                                        sprite.image = sprite_texture.clone();

                                        // Reset sprite color to white for proper texture display
                                        sprite.color = Color::WHITE;
                                        info!("🎨 Applied SF2 sprite texture for {:?} frame {} (sprite_id: {} → index: {})",
                                               anim_controller.fighter_id, anim_controller.current_frame, frame_data.sprite_id, texture_index);

                                    // Apply sprite scaling and flipping
                                    sprite.custom_size = Some(Vec2::new(
                                        16.0 * sprite_component.sprite_scale.x,
                                        16.0 * sprite_component.sprite_scale.y,
                                    ));
                                    sprite.flip_x = sprite_component.flip_x;

                                    // Apply frame offset
                                    if let Some(offset) = Some(frame_data.offset) {
                                        // Frame offset will be applied to transform in sync system
                                        // Store offset in a component if needed
                                    }
                                    } else {
                                        warn!("❌ No texture found for sprite ID {} at index {} (available: {})",
                                              frame_data.sprite_id, texture_index, sprite_textures.len());
                                        // Apply magenta color to indicate missing texture
                                        sprite.color = Color::srgb(1.0, 0.0, 1.0);
                                    }
                                } else {
                                    warn!("❌ Sprite ID {} not found in loaded sprite list", frame_data.sprite_id);
                                    // Apply magenta color to indicate missing texture
                                    sprite.color = Color::srgb(1.0, 0.0, 1.0);
                                }
                            } else {
                                warn!("❌ No sprite metadata found for sprite ID {}", frame_data.sprite_id);
                                // Apply red color to indicate metadata missing
                                sprite.color = Color::srgb(1.0, 0.0, 0.0);
                            }
                        } else {
                            warn!("❌ No frame data found for frame {}", anim_controller.current_frame);
                            // Apply yellow color to indicate frame data missing
                            sprite.color = Color::srgb(1.0, 1.0, 0.0);
                        }
                    } else {
                        warn!("❌ No sprite textures found for {:?}", anim_controller.fighter_id);
                        // Apply cyan color to indicate sprite textures missing
                        sprite.color = Color::srgb(0.0, 1.0, 1.0);
                    }
                } else {
                    warn!("❌ No fighter textures found for {:?} (loaded fighters: {:?})",
                          anim_controller.fighter_id,
                          sprite_manager.character_textures.keys().collect::<Vec<_>>());
                    // Apply blue color to indicate fighter textures missing
                    sprite.color = Color::srgb(0.0, 0.0, 1.0);
                }
            }
        }
    }
}

/// Process animation frame events
fn process_frame_events(
    anim_controller: &mut SF2AnimationController,
    current_frame: usize,
    fighter_id: FighterId,
    audio_playbook: &SF2AudioPlaybook,
    audio_events: &mut EventWriter<SF2AudioEvent>,
) {
    let mut unlock_animation = false;

    // Process events without borrowing conflicts
    for event in &mut anim_controller.frame_events {
        if event.frame_number == current_frame && !event.triggered {
            match &event.event_type {
                AnimationEventType::PlaySound(sound_name) => {
                    info!("Animation frame event: Play sound {} for fighter {:?}", sound_name, fighter_id);

                    // Try to get character-specific voice sample first
                    if let Some(sample_name) = audio_playbook.get_character_voice(fighter_id, sound_name) {
                        if audio_playbook.is_sample_available(sample_name) {
                            audio_events.send(SF2AudioEvent::PlaySFX {
                                sample_name: sample_name.clone(),
                                volume: Some(0.9),
                                position: None,
                            });
                            debug!("Playing character voice: {} for {}", sample_name, sound_name);
                        } else {
                            warn!("Character voice sample not available: {}", sample_name);
                        }
                    }
                    // Fallback to SFX if no character voice found
                    else if let Some(sample_name) = audio_playbook.get_sfx(sound_name) {
                        if audio_playbook.is_sample_available(sample_name) {
                            audio_events.send(SF2AudioEvent::PlaySFX {
                                sample_name: sample_name.clone(),
                                volume: Some(0.8),
                                position: None,
                            });
                            debug!("Playing SFX: {} for {}", sample_name, sound_name);
                        } else {
                            warn!("SFX sample not available: {}", sample_name);
                        }
                    } else {
                        warn!("No audio mapping found for sound: {}", sound_name);
                    }
                }
                AnimationEventType::SpawnEffect(effect_name) => {
                    info!("Animation frame event: Spawn effect {}", effect_name);
                    // TODO: Spawn visual effect
                }
                AnimationEventType::EnableHitbox => {
                    info!("Animation frame event: Enable hitbox");
                    // TODO: Enable hitbox component
                }
                AnimationEventType::DisableHitbox => {
                    info!("Animation frame event: Disable hitbox");
                    // TODO: Disable hitbox component
                }
                AnimationEventType::AllowStateChange => {
                    info!("Animation frame event: Allow state change");
                    unlock_animation = true;
                }
                AnimationEventType::TriggerSpecialMove => {
                    info!("Animation frame event: Trigger special move");
                    // TODO: Trigger special move event
                }
            }
            event.triggered = true;
        }
    }

    // Apply state changes after processing events
    if unlock_animation {
        anim_controller.unlock_animation();
    }
}

/// System to initialize animation controllers for new fighters
fn initialize_animation_controllers_system(
    mut commands: Commands,
    query: Query<(Entity, &Fighter), (Added<Fighter>, Without<SF2AnimationController>)>,
) {
    for (entity, fighter) in query.iter() {
        let mut anim_controller = SF2AnimationController::new(fighter.fighter_id);

        // Add frame events for basic attacks
        anim_controller.add_frame_event(1, AnimationEventType::EnableHitbox);
        anim_controller.add_frame_event(2, AnimationEventType::DisableHitbox);
        anim_controller.add_frame_event(2, AnimationEventType::AllowStateChange);

        // Add character-specific voice events for attacks
        match fighter.fighter_id {
            FighterId::Ryu => {
                // Ryu voice events for attacks
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("light_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("medium_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("heavy_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("hadoken".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("shoryuken".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("tatsumaki".to_string()));
            }
            FighterId::Ken => {
                // Ken voice events for attacks
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("light_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("medium_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("heavy_punch".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("hadoken".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("shoryuken".to_string()));
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("tatsumaki".to_string()));
            }
            _ => {
                // Default voice events for other characters
                anim_controller.add_frame_event(0, AnimationEventType::PlaySound("attack".to_string()));
            }
        }

        commands.entity(entity).insert(anim_controller);

        info!("Initialized animation controller for fighter {:?}", fighter.fighter_id);
    }
}
