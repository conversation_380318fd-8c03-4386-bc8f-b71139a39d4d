[package]
name = "sf2_game"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Street Fighter II game executable"

[[bin]]
name = "sf2"
path = "src/main.rs"

[dependencies]
bevy.workspace = true
sf2_engine = { path = "../sf2_engine" }
sf2_types = { path = "../sf2_types" }
sf2_assets = { path = "../sf2_assets" }
anyhow.workspace = true
env_logger.workspace = true
log.workspace = true

[features]
default = ["bevy/default"]
dev = ["bevy/dynamic_linking"]  # Faster compile times in development
